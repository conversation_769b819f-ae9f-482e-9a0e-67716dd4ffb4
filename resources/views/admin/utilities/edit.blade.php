@extends('layouts.admin')

@section('title', 'Edit Utility - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Utilities Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.utilities.index') }}">Utilities</a></li>
                    <li class="breadcrumb-item"><a
                            href="{{ route('admin.utilities.show', $utility) }}">{{ $utility->name }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Edit</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        Edit Utility: {{ $utility->name }}
                    </div>
                    <div class="card-options">
                        <a href="{{ route('admin.utilities.index') }}" class="btn btn-info btn-sm">
                            <i class="ri-arrow-left-line me-1"></i>Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.utilities.update', $utility) }}">
                        @csrf
                        @method('PUT')

                        <!-- Utility Name -->
                        <div class="mb-3">
                            <label for="name" class="form-label">Utility Name <span class="text-danger">*</span></label>
                            <input type="text" name="name" id="name" value="{{ old('name', $utility->name) }}"
                                required class="form-control @error('name') is-invalid @enderror"
                                placeholder="e.g., Electricity, Water, Internet">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea name="description" id="description" rows="4"
                                class="form-control @error('description') is-invalid @enderror"
                                placeholder="Describe the utility and its purpose...">{{ old('description', $utility->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Optional: Provide a detailed description of the utility.</div>
                        </div>



                        <!-- Hourly Rate -->
                        <div class="mb-3">
                            <label for="hourly_rate" class="form-label">Hourly Rate</label>
                            <div class="input-group">
                                <span class="input-group-text">XCG</span>
                                <input type="number" name="hourly_rate" id="hourly_rate"
                                    value="{{ old('hourly_rate', $utility->hourly_rate) }}" step="0.01" min="0"
                                    max="999999.99" class="form-control @error('hourly_rate') is-invalid @enderror"
                                    placeholder="0.00">
                                @error('hourly_rate')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="form-text">Optional: Set the hourly rate for this utility (leave blank if not
                                applicable)</div>
                        </div>

                        <!-- Status -->
                        <div class="mb-3">
                            <label class="form-label d-block mb-2">Status</label>
                            <div class="btn-group" role="group" aria-label="Utility status selection">
                                <input type="radio" class="btn-check" name="is_active" id="status_active"
                                       value="1" {{ old('is_active', $utility->is_active) ? 'checked' : '' }} autocomplete="off">
                                <label class="btn btn-outline-primary" for="status_active">
                                    <i class="ri-check-line me-1"></i>Active
                                </label>

                                <input type="radio" class="btn-check" name="is_active" id="status_inactive"
                                       value="0" {{ !old('is_active', $utility->is_active) ? 'checked' : '' }} autocomplete="off">
                                <label class="btn btn-outline-danger" for="status_inactive">
                                    <i class="ri-close-line me-1"></i>Inactive
                                </label>
                            </div>
                            <div class="form-text">Active utilities will be available for reservation assignment.</div>
                        </div>

                        <!-------------------------------------------------------------------------------------->
                        <x-icon-selector
                            :selected="old('icon_class', $utility->icon_class ?? 'ri-check-line')"
                            name="icon_class"
                            :required="true"
                            label="Select Icon"
                            id="utility-edit-icon-selector"
                        />
                        <!-------------------------------------------------------------------------------------->

                        <!-- Usage Information -->
                        @if ($utility->fields_count > 0)
                            <div class="mb-3">
                                <label class="form-label">Usage Information</label>
                                <div class="alert alert-info">
                                    <i class="ri-information-line me-2"></i>
                                    This utility is currently used by <strong>{{ $utility->fields_count }}</strong>
                                    field(s).
                                </div>
                            </div>
                        @endif

                        <!-- Form Actions -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="d-flex justify-content-end">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="ri-save-line me-1"></i>Update Utility
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Form validation
            const form = document.querySelector('form');
            form.addEventListener('submit', function(e) {
                const name = document.getElementById('name').value.trim();
                const iconClass = document.getElementById('icon_class').value.trim();

                if (!name) {
                    e.preventDefault();
                    alert('Please enter a utility name.');
                    document.getElementById('name').focus();
                    return;
                }

                if (!iconClass) {
                    e.preventDefault();
                    alert('Please enter an icon class.');
                    document.getElementById('icon_class').focus();
                    return;
                }
            });
        });
    </script>


@endpush
